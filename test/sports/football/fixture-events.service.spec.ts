import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureEventsService } from '../../../src/sports/football/services/fixture-events.service';
import { FixtureEvents } from '../../../src/sports/football/models/fixture-events.entity';
import { CacheService } from '../../../src/core/cache/cache.service';
import { UtilsService } from '../../../src/shared/utils/utils.service';

jest.mock('axios');

describe('FixtureEventsService', () => {
    let service: FixtureEventsService;
    let eventsRepository: Repository<FixtureEvents>;
    let cacheService: CacheService;
    let configService: ConfigService;

    const mockEventsRepository = {
        findOne: jest.fn(),
        save: jest.fn(),
    };

    const mockCacheService = {
        getCache: jest.fn(),
        setCache: jest.fn(),
    };

    const mockConfigService = {
        get: jest.fn(),
    };

    const mockUtilsService = {
        // Add any utils methods if needed
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                FixtureEventsService,
                {
                    provide: getRepositoryToken(FixtureEvents),
                    useValue: mockEventsRepository,
                },
                {
                    provide: CacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: ConfigService,
                    useValue: mockConfigService,
                },
                {
                    provide: UtilsService,
                    useValue: mockUtilsService,
                },
            ],
        }).compile();

        service = module.get<FixtureEventsService>(FixtureEventsService);
        eventsRepository = module.get<Repository<FixtureEvents>>(getRepositoryToken(FixtureEvents));
        cacheService = module.get<CacheService>(CacheService);
        configService = module.get<ConfigService>(ConfigService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getEvents', () => {
        const fixtureId = 1324147;
        const mockEvents = {
            fixtureId,
            events: [
                {
                    time: { elapsed: 23, extra: null },
                    team: { id: 33, name: 'Manchester United', logo: '/images/teams/33.png' },
                    player: { id: 276, name: 'Bruno Fernandes' },
                    assist: { id: 882, name: 'Marcus Rashford' },
                    type: 'Goal',
                    detail: 'Normal Goal'
                }
            ]
        };

        it('should return cached events if available', async () => {
            mockCacheService.getCache.mockResolvedValue(JSON.stringify(mockEvents));

            const result = await service.getEvents(fixtureId);

            expect(result).toEqual({ data: mockEvents, status: 200 });
            expect(mockCacheService.getCache).toHaveBeenCalledWith(`fixture_events_${fixtureId}`);
            expect(mockEventsRepository.findOne).not.toHaveBeenCalled();
        });

        it('should return events from database if not cached', async () => {
            mockCacheService.getCache.mockResolvedValue(null);
            mockEventsRepository.findOne.mockResolvedValue(mockEvents);

            const result = await service.getEvents(fixtureId);

            expect(result.data).toEqual(mockEvents);
            expect(result.status).toBe(200);
            expect(mockEventsRepository.findOne).toHaveBeenCalledWith({ where: { fixtureId } });
            expect(mockCacheService.setCache).toHaveBeenCalled();
        });

        it('should fetch from API if not in database', async () => {
            mockCacheService.getCache.mockResolvedValue(null);
            mockEventsRepository.findOne.mockResolvedValue(null);
            mockConfigService.get.mockImplementation((key) => {
                if (key === 'app.apiFootballUrl') return 'https://api.football.com';
                if (key === 'app.apiFootballKey') return 'test-key';
            });

            const mockApiResponse = {
                data: {
                    response: [
                        {
                            time: { elapsed: 23, extra: null },
                            team: { id: 33, name: 'Manchester United', logo: '/images/teams/33.png' },
                            player: { id: 276, name: 'Bruno Fernandes' },
                            assist: { id: 882, name: 'Marcus Rashford' },
                            type: 'Goal',
                            detail: 'Normal Goal'
                        }
                    ],
                    errors: {}
                }
            };

            (axios.get as jest.Mock).mockResolvedValue(mockApiResponse);
            mockEventsRepository.save.mockResolvedValue(mockEvents);

            const result = await service.getEvents(fixtureId);

            expect(result.status).toBe(200);
            expect(mockEventsRepository.save).toHaveBeenCalled();
        });

        it('should return empty events with message if no events available', async () => {
            mockCacheService.getCache.mockResolvedValue(null);
            mockEventsRepository.findOne.mockResolvedValue(null);
            mockConfigService.get.mockImplementation((key) => {
                if (key === 'app.apiFootballUrl') return 'https://api.football.com';
                if (key === 'app.apiFootballKey') return 'test-key';
            });

            const mockApiResponse = {
                data: {
                    response: [],
                    errors: {}
                }
            };

            (axios.get as jest.Mock).mockResolvedValue(mockApiResponse);

            const result = await service.getEvents(fixtureId);

            expect(result.data).toEqual({ fixtureId, events: [] });
            expect(result.status).toBe(200);
            expect(result.message).toBe(`No events available for fixture ${fixtureId}`);
        });
    });
});
