import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFixturePlayersTable1748000000004 implements MigrationInterface {
    name = 'CreateFixturePlayersTable1748000000004';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create fixture_players table
        await queryRunner.query(`
            CREATE TABLE "fixture_players" (
                "id" SERIAL NOT NULL,
                "fixtureId" integer NOT NULL,
                "lineups" jsonb NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "PK_fixture_players_id" PRIMARY KEY ("id")
            )
        `);

        // Create index on fixtureId for faster queries
        await queryRunner.query(`
            CREATE INDEX "IDX_fixture_players_fixtureId" ON "fixture_players" ("fixtureId")
        `);

        // Create unique constraint on fixtureId (one players record per fixture)
        await queryRunner.query(`
            CREATE UNIQUE INDEX "UQ_fixture_players_fixtureId" ON "fixture_players" ("fixtureId")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`DROP INDEX "UQ_fixture_players_fixtureId"`);
        await queryRunner.query(`DROP INDEX "IDX_fixture_players_fixtureId"`);
        
        // Drop table
        await queryRunner.query(`DROP TABLE "fixture_players"`);
    }
}
