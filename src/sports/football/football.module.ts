import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FixtureService } from './services/fixture.service';
import { FixtureStatisticsService } from './services/fixture-statistics.service';
import { FixtureEventsService } from './services/fixture-events.service';
import { FixturePlayersService } from './services/fixture-players.service';
import { LeagueService } from './services/league.service';
import { TeamService } from './services/team.service';
import { TeamStatisticsService } from './services/team-statistics.service';
import { PlayerService } from './services/player.service';
import { StandingService } from './services/standing.service';
import { SeasonSyncModule } from './season-sync.module';
import { Fixture } from './models/fixture.entity';
import { League } from './models/league.entity';
import { Team } from './models/team.entity';
import { FixtureStatistics } from './models/fixture-statistics.entity';
import { FixtureEvents } from './models/fixture-events.entity';
import { FixturePlayers } from './models/fixture-players.entity';
import { TeamStatistics } from './models/team-statistics.entity';
import { Player } from './models/player.entity';
import { PlayerStatistics } from './models/player-statistics.entity';
import { Standing } from './models/standing.entity';


// Base Football Module - Contains shared services and entities
@Module({
  imports: [
    TypeOrmModule.forFeature([Fixture, League, Team, FixtureStatistics, FixtureEvents, FixturePlayers, TeamStatistics, Player, PlayerStatistics, Standing]),
    SeasonSyncModule,
  ],
  providers: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    FixtureEventsService,
    FixturePlayersService,
    TeamStatisticsService,
    PlayerService,
    StandingService,
  ],
  exports: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    FixtureEventsService,
    FixturePlayersService,
    TeamStatisticsService,
    PlayerService,
    StandingService,
    SeasonSyncModule,
    TypeOrmModule,
  ],
})
export class FootballModule { }