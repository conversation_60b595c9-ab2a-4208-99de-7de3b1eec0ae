import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixturePlayers } from '../models/fixture-players.entity';
import { FixturePlayersDto } from '../models/fixture-players.dto';
import { Fixture } from '../models/fixture.entity';
import { CacheService } from '../../../core';
import { UtilsService } from '../../../shared';

@Injectable()
export class FixturePlayersService {
    private readonly logger = new Logger(FixturePlayersService.name);

    constructor(
        @InjectRepository(FixturePlayers)
        private readonly playersRepository: Repository<FixturePlayers>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
    ) {}

    /**
     * Get players/lineups for a fixture by external ID with smart caching based on fixture status
     * @param externalId - Fixture external ID
     * @returns Fixture players/lineups
     */
    async getPlayers(externalId: number): Promise<{ data: FixturePlayersDto; status: number; message?: string }> {
        // 🔍 STEP 1: Get fixture info to determine status and caching strategy
        const fixture = await this.fixtureRepository.findOne({ where: { externalId } });
        if (!fixture) {
            this.logger.warn(`Fixture ${externalId} not found`);
            return { 
                data: { fixtureId: externalId, lineups: [] }, 
                status: 200, 
                message: `Fixture ${externalId} not found` 
            };
        }

        const fixtureStatus = fixture.data.status;
        const isLiveMatch = ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'LIVE'].includes(fixtureStatus);
        const isFinishedMatch = ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(fixtureStatus);
        const isUpcomingMatch = ['NS', 'TBD'].includes(fixtureStatus);

        // 🎯 STEP 2: Smart caching strategy based on fixture status
        let cacheTTL = 300; // Default 5 minutes
        let shouldFetchFromAPI = false;

        if (isFinishedMatch) {
            cacheTTL = 3600; // 1 hour for finished matches (lineups won't change)
        } else if (isLiveMatch) {
            cacheTTL = 600; // 10 minutes for live matches (lineups rarely change during match)
        } else if (isUpcomingMatch) {
            cacheTTL = 1800; // 30 minutes for upcoming matches (lineups may be announced)
            shouldFetchFromAPI = true; // Check for lineup announcements
        }

        const cacheKey = `fixture_players_${externalId}`;
        
        // 🚀 STEP 3: Check cache first (except for upcoming matches that might have new lineups)
        if (!shouldFetchFromAPI) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning players from cache for fixture ${externalId} (status: ${fixtureStatus})`);
                return { data: JSON.parse(cached), status: 200 };
            }
        }

        // 🔄 STEP 4: Get players from DB or API
        let players = await this.playersRepository.findOne({ where: { fixtureId: externalId } });
        
        // For upcoming matches or if no players in DB, fetch from API
        if (shouldFetchFromAPI || !players) {
            this.logger.debug(`Fetching fresh players from API for fixture ${externalId} (status: ${fixtureStatus})`);
            const apiPlayers = await this.fetchFromApi(externalId);
            
            if (apiPlayers && apiPlayers.lineups.length > 0) {
                // Update or create players in DB
                if (players) {
                    players.lineups = apiPlayers.lineups;
                    players.updatedAt = new Date();
                } else {
                    players = apiPlayers;
                }
                await this.playersRepository.save(players);
                this.logger.debug(`Updated players in DB for fixture ${externalId}`);
            }
        }

        // 📊 STEP 5: Prepare response
        if (!players || players.lineups.length === 0) {
            const emptyResponse = { fixtureId: externalId, lineups: [] };
            const message = isUpcomingMatch 
                ? `Lineups not announced yet for fixture ${externalId}`
                : `No lineups available for fixture ${externalId}`;
            
            // Cache empty results too (with shorter TTL for upcoming matches)
            await this.cacheService.setCache(cacheKey, JSON.stringify(emptyResponse), isUpcomingMatch ? 300 : cacheTTL);
            
            return { data: emptyResponse, status: 200, message };
        }

        const response: FixturePlayersDto = {
            fixtureId: players.fixtureId,
            lineups: players.lineups,
        };

        // 💾 STEP 6: Cache with smart TTL based on fixture status
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), cacheTTL);
        this.logger.debug(`Cached players for fixture ${externalId} (status: ${fixtureStatus}, TTL: ${cacheTTL}s, lineups: ${players.lineups.length})`);

        return { data: response, status: 200 };
    }

    /**
     * Fetch players/lineups from external API with retry
     * @param externalId - Fixture external ID
     * @returns Fixture players/lineups
     */
    private async fetchFromApi(externalId: number): Promise<FixturePlayers | null> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/lineups`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return null;
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No lineups data returned from API for fixture ${externalId}`);
                return null;
            }

            const players = new FixturePlayers();
            players.fixtureId = externalId;
            players.lineups = response.data.response.map((lineup: any) => ({
                team: {
                    id: lineup.team.id,
                    name: lineup.team.name,
                    logo: lineup.team.logo,
                    colors: lineup.team.colors ? {
                        player: lineup.team.colors.player ? {
                            primary: lineup.team.colors.player.primary,
                            number: lineup.team.colors.player.number,
                            border: lineup.team.colors.player.border,
                        } : undefined,
                        goalkeeper: lineup.team.colors.goalkeeper ? {
                            primary: lineup.team.colors.goalkeeper.primary,
                            number: lineup.team.colors.goalkeeper.number,
                            border: lineup.team.colors.goalkeeper.border,
                        } : undefined,
                    } : undefined,
                },
                formation: lineup.formation,
                startXI: lineup.startXI?.map((player: any) => ({
                    id: player.player.id,
                    name: player.player.name,
                    number: player.player.number,
                    position: player.player.pos,
                    grid: player.player.grid,
                })) || [],
                substitutes: lineup.substitutes?.map((player: any) => ({
                    id: player.player.id,
                    name: player.player.name,
                    number: player.player.number,
                    position: player.player.pos,
                    grid: player.player.grid,
                })) || [],
                coach: lineup.coach ? {
                    id: lineup.coach.id,
                    name: lineup.coach.name,
                    photo: lineup.coach.photo,
                } : undefined,
            }));

            return players;

        } catch (error) {
            this.logger.error(`Failed to fetch players for fixture ${externalId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Execute API call with retry logic
     * @param apiCall - Function to execute
     * @returns API response
     */
    private async executeWithRetry<T>(apiCall: () => Promise<T>, maxRetries = 3): Promise<T> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }
                this.logger.warn(`API call attempt ${attempt} failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        throw new Error('Max retries exceeded');
    }
}
