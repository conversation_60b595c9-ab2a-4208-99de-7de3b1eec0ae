import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureLineup } from '../models/fixture-players.entity';
import { FixturePlayersDto } from '../models/fixture-players.dto';
import { Fixture } from '../models/fixture.entity';
import { Player } from '../models/player.entity';
import { CacheService } from '../../../core';
import { UtilsService } from '../../../shared';

@Injectable()
export class FixturePlayersService {
    private readonly logger = new Logger(FixturePlayersService.name);

    constructor(
        @InjectRepository(FixtureLineup)
        private readonly lineupRepository: Repository<FixtureLineup>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
    ) { }

    /**
     * Get players/lineups for a fixture by external ID with smart caching based on fixture status
     * @param externalId - Fixture external ID
     * @returns Fixture players/lineups
     */
    async getPlayers(externalId: number): Promise<{ data: FixturePlayersDto; status: number; message?: string }> {
        // 🔍 STEP 1: Get fixture info to determine status and caching strategy
        const fixture = await this.fixtureRepository.findOne({ where: { externalId } });
        if (!fixture) {
            this.logger.warn(`Fixture ${externalId} not found`);
            return {
                data: { fixtureId: externalId, lineups: [] },
                status: 200,
                message: `Fixture ${externalId} not found`
            };
        }

        const fixtureStatus = fixture.data.status;
        const isLiveMatch = ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'LIVE'].includes(fixtureStatus);
        const isFinishedMatch = ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(fixtureStatus);
        const isUpcomingMatch = ['NS', 'TBD'].includes(fixtureStatus);

        // 🎯 STEP 2: Smart caching strategy based on fixture status
        let cacheTTL = 300; // Default 5 minutes
        let shouldFetchFromAPI = false;

        if (isFinishedMatch) {
            cacheTTL = 3600; // 1 hour for finished matches (lineups won't change)
        } else if (isLiveMatch) {
            cacheTTL = 600; // 10 minutes for live matches (lineups rarely change during match)
        } else if (isUpcomingMatch) {
            cacheTTL = 1800; // 30 minutes for upcoming matches (lineups may be announced)
            shouldFetchFromAPI = true; // Check for lineup announcements
        }

        const cacheKey = `fixture_players_${externalId}`;

        // 🚀 STEP 3: Check cache first (except for upcoming matches that might have new lineups)
        if (!shouldFetchFromAPI) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning players from cache for fixture ${externalId} (status: ${fixtureStatus})`);
                return { data: JSON.parse(cached), status: 200 };
            }
        }

        // 🔄 STEP 4: Get lineups from DB or API
        let lineups = await this.lineupRepository.find({
            where: { fixtureId: externalId },
            relations: ['player']
        });

        // For upcoming matches or if no lineups in DB, fetch from API
        if (shouldFetchFromAPI || lineups.length === 0) {
            this.logger.debug(`Fetching fresh lineups from API for fixture ${externalId} (status: ${fixtureStatus})`);
            const apiLineups = await this.fetchFromApi(externalId);

            if (apiLineups && apiLineups.length > 0) {
                // Delete existing lineups for this fixture
                await this.lineupRepository.delete({ fixtureId: externalId });

                // Save new lineups
                await this.lineupRepository.save(apiLineups);
                this.logger.debug(`Updated lineups in DB for fixture ${externalId}`);

                // Reload with relations
                lineups = await this.lineupRepository.find({
                    where: { fixtureId: externalId },
                    relations: ['player']
                });
            }
        }

        // 📊 STEP 5: Prepare response
        if (!lineups || lineups.length === 0) {
            const emptyResponse = { fixtureId: externalId, lineups: [] };
            const message = isUpcomingMatch
                ? `Lineups not announced yet for fixture ${externalId}`
                : `No lineups available for fixture ${externalId}`;

            // Cache empty results too (with shorter TTL for upcoming matches)
            await this.cacheService.setCache(cacheKey, JSON.stringify(emptyResponse), isUpcomingMatch ? 300 : cacheTTL);

            return { data: emptyResponse, status: 200, message };
        }

        // Group lineups by team
        const teamLineups = this.groupLineupsByTeam(lineups);

        const response: FixturePlayersDto = {
            fixtureId: externalId,
            lineups: teamLineups,
        };

        // 💾 STEP 6: Cache with smart TTL based on fixture status
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), cacheTTL);
        this.logger.debug(`Cached players for fixture ${externalId} (status: ${fixtureStatus}, TTL: ${cacheTTL}s, teams: ${teamLineups.length})`);

        return { data: response, status: 200 };
    }

    /**
     * Get optimized lineups view for a fixture by external ID
     * @param externalId - Fixture external ID
     * @returns Optimized lineups data
     */
    async getLineups(externalId: number): Promise<{ data: any; status: number; message?: string }> {
        // 🔍 STEP 1: Get fixture info to determine status and caching strategy
        const fixture = await this.fixtureRepository.findOne({ where: { externalId } });
        if (!fixture) {
            this.logger.warn(`Fixture ${externalId} not found`);
            return {
                data: { fixtureId: externalId, teams: [] },
                status: 200,
                message: `Fixture ${externalId} not found`
            };
        }

        const fixtureStatus = fixture.data.status;
        const isUpcomingMatch = ['NS', 'TBD'].includes(fixtureStatus);

        const cacheKey = `fixture_lineups_optimized_${externalId}`;

        // 🚀 STEP 2: Check cache first
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning optimized lineups from cache for fixture ${externalId}`);
            return { data: JSON.parse(cached), status: 200 };
        }

        // 🔄 STEP 3: Get lineups from DB
        const lineups = await this.lineupRepository.find({
            where: { fixtureId: externalId },
            relations: ['player']
        });

        // 📊 STEP 4: Prepare optimized response
        if (!lineups || lineups.length === 0) {
            const emptyResponse = { fixtureId: externalId, teams: [] };
            const message = isUpcomingMatch
                ? `Lineups not announced yet for fixture ${externalId}`
                : `No lineups available for fixture ${externalId}`;

            // Cache empty results
            await this.cacheService.setCache(cacheKey, JSON.stringify(emptyResponse), 300);

            return { data: emptyResponse, status: 200, message };
        }

        // Group and optimize lineups
        const optimizedTeams = this.createOptimizedLineups(lineups);

        const response = {
            fixtureId: externalId,
            teams: optimizedTeams,
        };

        // 💾 STEP 5: Cache with 10 minutes TTL
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 600);
        this.logger.debug(`Cached optimized lineups for fixture ${externalId} (TTL: 600s, teams: ${optimizedTeams.length})`);

        return { data: response, status: 200 };
    }

    /**
     * Create optimized lineups structure for UI display
     * @param lineups - Array of lineup records
     * @returns Optimized lineups structure
     */
    private createOptimizedLineups(lineups: FixtureLineup[]): any[] {
        const teams = new Map();

        lineups.forEach(lineup => {
            if (!teams.has(lineup.teamId)) {
                teams.set(lineup.teamId, {
                    id: lineup.teamId,
                    name: lineup.teamName,
                    logo: lineup.teamLogo,
                    formation: lineup.formation,
                    colors: {
                        primary: lineup.teamColors?.player?.primary || '#000000',
                        secondary: lineup.teamColors?.player?.number || '#ffffff',
                        goalkeeper: lineup.teamColors?.goalkeeper?.primary || '#ffff00',
                    },
                    startingXI: [],
                    substitutes: [],
                    coach: lineup.coach?.name || null,
                    coachPhoto: lineup.coach?.photo || null,
                });
            }

            const team = teams.get(lineup.teamId);
            const playerData = {
                name: lineup.playerName,
                number: lineup.playerNumber,
                position: this.mapPosition(lineup.playerPosition),
                grid: lineup.playerGrid,
            };

            if (lineup.isStarting) {
                team.startingXI.push(playerData);
            } else {
                team.substitutes.push(playerData);
            }
        });

        return Array.from(teams.values());
    }

    /**
     * Map position codes to readable names
     * @param position - Position code from API
     * @returns Readable position name
     */
    private mapPosition(position: string): string {
        const positionMap: { [key: string]: string } = {
            'G': 'GK',
            'D': 'DEF',
            'M': 'MID',
            'F': 'FWD',
        };

        return positionMap[position] || position;
    }

    /**
     * Group lineups by team
     * @param lineups - Array of lineup records
     * @returns Grouped lineups by team
     */
    private groupLineupsByTeam(lineups: FixtureLineup[]): any[] {
        const teams = new Map();

        lineups.forEach(lineup => {
            if (!teams.has(lineup.teamId)) {
                teams.set(lineup.teamId, {
                    team: {
                        id: lineup.teamId,
                        name: lineup.teamName,
                        logo: lineup.teamLogo,
                        colors: lineup.teamColors,
                    },
                    formation: lineup.formation,
                    startXI: [],
                    substitutes: [],
                    coach: lineup.coach,
                });
            }

            const team = teams.get(lineup.teamId);
            const playerData = {
                id: lineup.playerId,
                name: lineup.playerName,
                number: lineup.playerNumber,
                position: lineup.playerPosition,
                grid: lineup.playerGrid,
            };

            if (lineup.isStarting) {
                team.startXI.push(playerData);
            } else {
                team.substitutes.push(playerData);
            }
        });

        return Array.from(teams.values());
    }

    /**
     * Fetch players/lineups from external API with retry
     * @param externalId - Fixture external ID
     * @returns Array of FixtureLineup entities
     */
    private async fetchFromApi(externalId: number): Promise<FixtureLineup[] | null> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/lineups`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return null;
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No lineups data returned from API for fixture ${externalId}`);
                return null;
            }

            const lineupEntities: FixtureLineup[] = [];

            for (const lineup of response.data.response) {
                const teamColors = lineup.team.colors ? {
                    player: lineup.team.colors.player ? {
                        primary: lineup.team.colors.player.primary,
                        number: lineup.team.colors.player.number,
                        border: lineup.team.colors.player.border,
                    } : undefined,
                    goalkeeper: lineup.team.colors.goalkeeper ? {
                        primary: lineup.team.colors.goalkeeper.primary,
                        number: lineup.team.colors.goalkeeper.number,
                        border: lineup.team.colors.goalkeeper.border,
                    } : undefined,
                } : null;

                const coach = lineup.coach ? {
                    id: lineup.coach.id,
                    name: lineup.coach.name,
                    photo: lineup.coach.photo,
                } : null;

                // Process starting XI
                if (lineup.startXI) {
                    for (const playerData of lineup.startXI) {
                        const player = await this.ensurePlayerExists(playerData.player);

                        const lineupEntity = new FixtureLineup();
                        lineupEntity.fixtureId = externalId;
                        lineupEntity.teamId = lineup.team.id;
                        lineupEntity.teamName = lineup.team.name;
                        lineupEntity.teamLogo = lineup.team.logo;
                        lineupEntity.formation = lineup.formation;
                        lineupEntity.playerId = player.id;
                        lineupEntity.playerName = player.name;
                        lineupEntity.playerNumber = playerData.player.number;
                        lineupEntity.playerPosition = playerData.player.pos;
                        lineupEntity.playerGrid = playerData.player.grid;
                        lineupEntity.isStarting = true;
                        lineupEntity.teamColors = teamColors;
                        lineupEntity.coach = coach;

                        lineupEntities.push(lineupEntity);
                    }
                }

                // Process substitutes
                if (lineup.substitutes) {
                    for (const playerData of lineup.substitutes) {
                        const player = await this.ensurePlayerExists(playerData.player);

                        const lineupEntity = new FixtureLineup();
                        lineupEntity.fixtureId = externalId;
                        lineupEntity.teamId = lineup.team.id;
                        lineupEntity.teamName = lineup.team.name;
                        lineupEntity.teamLogo = lineup.team.logo;
                        lineupEntity.formation = lineup.formation;
                        lineupEntity.playerId = player.id;
                        lineupEntity.playerName = player.name;
                        lineupEntity.playerNumber = playerData.player.number;
                        lineupEntity.playerPosition = playerData.player.pos;
                        lineupEntity.playerGrid = playerData.player.grid;
                        lineupEntity.isStarting = false;
                        lineupEntity.teamColors = teamColors;
                        lineupEntity.coach = coach;

                        lineupEntities.push(lineupEntity);
                    }
                }
            }

            return lineupEntities;

        } catch (error) {
            this.logger.error(`Failed to fetch players for fixture ${externalId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Ensure player exists in database, create if not exists
     * @param playerData - Player data from API
     * @returns Player entity
     */
    private async ensurePlayerExists(playerData: any): Promise<Player> {
        let player = await this.playerRepository.findOne({
            where: { externalId: playerData.id }
        });

        if (!player) {
            player = new Player();
            player.externalId = playerData.id;
            player.name = playerData.name;
            // These fields are nullable in entity, so we can leave them undefined
            player.injured = false;
            player.timestamp = Date.now();

            player = await this.playerRepository.save(player);
            this.logger.debug(`Created new player: ${player.name} (${player.externalId})`);
        }

        return player;
    }

    /**
     * Execute API call with retry logic
     * @param apiCall - Function to execute
     * @returns API response
     */
    private async executeWithRetry<T>(apiCall: () => Promise<T>, maxRetries = 3): Promise<T> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }
                this.logger.warn(`API call attempt ${attempt} failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        throw new Error('Max retries exceeded');
    }
}
