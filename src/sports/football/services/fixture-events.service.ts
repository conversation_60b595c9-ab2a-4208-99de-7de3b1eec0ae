import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureEvents } from '../models/fixture-events.entity';
import { FixtureEventsDto } from '../models/fixture-events.dto';
import { Fixture } from '../models/fixture.entity';
import { CacheService } from '../../../core';
import { UtilsService } from '../../../shared';

@Injectable()
export class FixtureEventsService {
    private readonly logger = new Logger(FixtureEventsService.name);

    constructor(
        @InjectRepository(FixtureEvents)
        private readonly eventsRepository: Repository<FixtureEvents>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
    ) { }

    /**
     * Get events for a fixture by external ID with smart caching based on fixture status
     * @param externalId - Fixture external ID
     * @returns Fixture events
     */
    async getEvents(externalId: number): Promise<{ data: FixtureEventsDto; status: number; message?: string }> {
        // 🔍 STEP 1: Get fixture info to determine status and caching strategy
        const fixture = await this.fixtureRepository.findOne({ where: { externalId } });
        if (!fixture) {
            this.logger.warn(`Fixture ${externalId} not found`);
            return {
                data: { fixtureId: externalId, events: [] },
                status: 200,
                message: `Fixture ${externalId} not found`
            };
        }

        const fixtureStatus = fixture.data.status;
        const isLiveMatch = ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'LIVE'].includes(fixtureStatus);
        const isFinishedMatch = ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(fixtureStatus);
        const isUpcomingMatch = ['NS', 'TBD'].includes(fixtureStatus);

        // 🎯 STEP 2: Smart caching strategy based on fixture status
        let cacheTTL = 30; // Default 30 seconds
        let shouldFetchFromAPI = false;

        if (isFinishedMatch) {
            cacheTTL = 3600; // 1 hour for finished matches (events won't change)
        } else if (isLiveMatch) {
            cacheTTL = 10; // 10 seconds for live matches (events change frequently)
            shouldFetchFromAPI = true; // Always try to get latest data for live matches
        } else if (isUpcomingMatch) {
            cacheTTL = 300; // 5 minutes for upcoming matches (unlikely to have events)
        }

        const cacheKey = `fixture_events_${externalId}`;

        // 🚀 STEP 3: Check cache first (except for live matches that need fresh data)
        if (!shouldFetchFromAPI) {
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning events from cache for fixture ${externalId} (status: ${fixtureStatus})`);
                return { data: JSON.parse(cached), status: 200 };
            }
        }

        // 🔄 STEP 4: Get events from DB or API
        let events = await this.eventsRepository.findOne({ where: { fixtureId: externalId } });

        // For live matches or if no events in DB, fetch from API
        if (shouldFetchFromAPI || !events) {
            this.logger.debug(`Fetching fresh events from API for fixture ${externalId} (status: ${fixtureStatus})`);
            const apiEvents = await this.fetchFromApi(externalId);

            if (apiEvents && apiEvents.events.length > 0) {
                // Update or create events in DB
                if (events) {
                    events.events = apiEvents.events;
                    events.updatedAt = new Date();
                } else {
                    events = apiEvents;
                }
                await this.eventsRepository.save(events);
                this.logger.debug(`Updated events in DB for fixture ${externalId}`);
            }
        }

        // 📊 STEP 5: Prepare response
        if (!events || events.events.length === 0) {
            const emptyResponse = { fixtureId: externalId, events: [] };
            const message = isUpcomingMatch
                ? `No events yet for upcoming fixture ${externalId}`
                : `No events available for fixture ${externalId}`;

            // Cache empty results too (with shorter TTL for upcoming matches)
            await this.cacheService.setCache(cacheKey, JSON.stringify(emptyResponse), isUpcomingMatch ? 60 : cacheTTL);

            return { data: emptyResponse, status: 200, message };
        }

        const response: FixtureEventsDto = {
            fixtureId: events.fixtureId,
            events: events.events,
        };

        // 💾 STEP 6: Cache with smart TTL based on fixture status
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), cacheTTL);
        this.logger.debug(`Cached events for fixture ${externalId} (status: ${fixtureStatus}, TTL: ${cacheTTL}s, events: ${events.events.length})`);

        return { data: response, status: 200 };
    }

    /**
     * Fetch events from external API with retry
     * @param externalId - Fixture external ID
     * @returns Fixture events
     */
    private async fetchFromApi(externalId: number): Promise<FixtureEvents | null> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/events`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return null;
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No events data returned from API for fixture ${externalId}`);
                return null;
            }

            const events = new FixtureEvents();
            events.fixtureId = externalId;
            events.events = response.data.response.map((event: any) => ({
                time: {
                    elapsed: event.time.elapsed,
                    extra: event.time.extra || null,
                },
                team: {
                    id: event.team.id,
                    name: event.team.name,
                    logo: event.team.logo,
                },
                player: {
                    id: event.player.id,
                    name: event.player.name,
                },
                assist: event.assist ? {
                    id: event.assist.id || null,
                    name: event.assist.name || null,
                } : null,
                type: event.type,
                detail: event.detail,
            }));

            return events;

        } catch (error) {
            this.logger.error(`Failed to fetch events for fixture ${externalId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Execute API call with retry logic
     * @param apiCall - Function to execute
     * @returns API response
     */
    private async executeWithRetry<T>(apiCall: () => Promise<T>, maxRetries = 3): Promise<T> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }
                this.logger.warn(`API call attempt ${attempt} failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        throw new Error('Max retries exceeded');
    }
}
