import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureEvents } from '../models/fixture-events.entity';
import { FixtureEventsDto } from '../models/fixture-events.dto';
import { CacheService } from '../../../core';
import { UtilsService } from '../../../shared';

@Injectable()
export class FixtureEventsService {
    private readonly logger = new Logger(FixtureEventsService.name);

    constructor(
        @InjectRepository(FixtureEvents)
        private readonly eventsRepository: Repository<FixtureEvents>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
    ) {}

    /**
     * Get events for a fixture by external ID
     * @param externalId - Fixture external ID
     * @returns Fixture events
     */
    async getEvents(externalId: number): Promise<{ data: FixtureEventsDto; status: number; message?: string }> {
        const cacheKey = `fixture_events_${externalId}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning events from cache for key: ${cacheKey}`);
            return { data: JSON.parse(cached), status: 200 };
        }

        let events = await this.eventsRepository.findOne({ where: { fixtureId: externalId } });
        if (!events) {
            this.logger.debug(`No events found in DB for fixture ${externalId}, fetching from API`);
            events = await this.fetchFromApi(externalId);
            if (events && events.events.length > 0) {
                await this.eventsRepository.save(events);
                this.logger.debug(`Saved events to DB for fixture ${externalId}`);
            }
        }

        if (!events || events.events.length === 0) {
            this.logger.warn(`No events available for fixture ${externalId}`);
            return { 
                data: { fixtureId: externalId, events: [] }, 
                status: 200, 
                message: `No events available for fixture ${externalId}` 
            };
        }

        const response: FixtureEventsDto = {
            fixtureId: events.fixtureId,
            events: events.events,
        };

        // Cache for 30 seconds (events change frequently during live matches)
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 30);
        this.logger.debug(`Cached events for fixture ${externalId} (TTL: 30s)`);

        return { data: response, status: 200 };
    }

    /**
     * Fetch events from external API with retry
     * @param externalId - Fixture external ID
     * @returns Fixture events
     */
    private async fetchFromApi(externalId: number): Promise<FixtureEvents | null> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/events`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return null;
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No events data returned from API for fixture ${externalId}`);
                return null;
            }

            const events = new FixtureEvents();
            events.fixtureId = externalId;
            events.events = response.data.response.map((event: any) => ({
                time: {
                    elapsed: event.time.elapsed,
                    extra: event.time.extra || null,
                },
                team: {
                    id: event.team.id,
                    name: event.team.name,
                    logo: event.team.logo,
                },
                player: {
                    id: event.player.id,
                    name: event.player.name,
                },
                assist: event.assist ? {
                    id: event.assist.id || null,
                    name: event.assist.name || null,
                } : null,
                type: event.type,
                detail: event.detail,
            }));

            return events;

        } catch (error) {
            this.logger.error(`Failed to fetch events for fixture ${externalId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Execute API call with retry logic
     * @param apiCall - Function to execute
     * @returns API response
     */
    private async executeWithRetry<T>(apiCall: () => Promise<T>, maxRetries = 3): Promise<T> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }
                this.logger.warn(`API call attempt ${attempt} failed, retrying...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        throw new Error('Max retries exceeded');
    }
}
