import { Enti<PERSON>, Column, PrimaryGeneratedColumn } from 'typeorm';

interface PlayerData {
    id: number;
    name: string;
    number: number;
    position: string;
    grid?: string;
}

interface LineupData {
    team: {
        id: number;
        name: string;
        logo: string;
        colors?: {
            player?: {
                primary: string;
                number: string;
                border: string;
            };
            goalkeeper?: {
                primary: string;
                number: string;
                border: string;
            };
        };
    };
    formation: string;
    startXI: PlayerData[];
    substitutes: PlayerData[];
    coach?: {
        id: number;
        name: string;
        photo: string;
    };
}

@Entity('fixture_players')
export class FixturePlayers {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    fixtureId: number;

    @Column({ type: 'jsonb' })
    lineups: LineupData[];

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
