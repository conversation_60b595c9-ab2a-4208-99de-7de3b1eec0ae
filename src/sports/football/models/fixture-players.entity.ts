import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyTo<PERSON>ne, Join<PERSON>olumn, Index } from 'typeorm';
import { Player } from './player.entity';

@Entity('fixture_lineups')
export class FixtureLineup {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column()
    fixtureId: number;

    @Index()
    @Column()
    teamId: number;

    @Column()
    teamName: string;

    @Column({ nullable: true })
    teamLogo: string;

    @Column({ nullable: true })
    formation: string;

    @ManyToOne(() => Player)
    @JoinColumn({ name: 'playerId' })
    player: Player;

    @Index()
    @Column()
    playerId: number;

    @Column()
    playerName: string;

    @Column({ nullable: true })
    playerNumber: number;

    @Column({ nullable: true })
    playerPosition: string;

    @Column({ nullable: true })
    playerGrid: string;

    @Column({ type: 'boolean', default: false })
    isStarting: boolean; // true for startXI, false for substitutes

    @Column({ type: 'jsonb', nullable: true })
    teamColors: {
        player?: {
            primary: string;
            number: string;
            border: string;
        };
        goalkeeper?: {
            primary: string;
            number: string;
            border: string;
        };
    };

    @Column({ type: 'jsonb', nullable: true })
    coach: {
        id: number;
        name: string;
        photo: string;
    };

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
