import { IsInt, <PERSON><PERSON><PERSON>y, IsObject, IsString, IsOptional, IsNumber } from 'class-validator';

export class PlayerDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsNumber()
    number: number;

    @IsString()
    position: string;

    @IsString()
    @IsOptional()
    grid?: string;
}

export class CoachDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsString()
    photo: string;
}

export class TeamColorsDto {
    @IsObject()
    @IsOptional()
    player?: {
        primary: string;
        number: string;
        border: string;
    };

    @IsObject()
    @IsOptional()
    goalkeeper?: {
        primary: string;
        number: string;
        border: string;
    };
}

export class LineupDto {
    @IsObject()
    team: {
        id: number;
        name: string;
        logo: string;
        colors?: TeamColorsDto;
    };

    @IsString()
    formation: string;

    @IsArray()
    startXI: PlayerDto[];

    @IsArray()
    substitutes: PlayerDto[];

    @IsObject()
    @IsOptional()
    coach?: <PERSON>D<PERSON>;
}

export class FixturePlayersDto {
    @IsInt()
    fixtureId: number;

    @IsArray()
    lineups: LineupDto[];
}
